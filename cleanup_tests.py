#!/usr/bin/env python3
"""
Helper script to clean up the original test files from the root directory
after they have been successfully moved to the tests directory structure.
"""

import os
import sys
from pathlib import Path

# Verify if the user wants to proceed with deletion
print("This script will remove the original test files from the root directory.")
print("The test files have been moved to their respective subdirectories in the tests/ folder.")
confirm = input("Are you sure you want to proceed? [y/N]: ")

if confirm.lower() != "y":
    print("Operation cancelled. No files were deleted.")
    sys.exit(0)

# List of test files to remove from root
test_files = [
    "test_comprehensive_simple.py",
    "test_comprehensive_svg_rendering.py",
    "test_cross_browser_compatibility.py",
    "test_error_handling.py",
    "test_error_handling_simple.py",
    "test_export_enhancements.py",
    "test_minimal_svg.py",
    "test_performance.py",
    "test_remote_rendering.py",
    "test_svg_renderer.py",
    "test_svg_rendering.py",
    "test_svg_validation.py",
    "test_theme_support.py",
]

# Remove each file if it exists
for file_name in test_files:
    file_path = Path(file_name)
    if file_path.exists():
        os.remove(file_path)
        print(f"Deleted: {file_name}")
    else:
        print(f"File not found, already removed: {file_name}")

print("\nCleanup complete.")
print("All test files have been organized into the tests/ directory structure.")
