#!/usr/bin/env python3
"""
Helper script to fix import paths in moved test files.
"""

import os
import glob
import re

# Directories to process
dirs = [
    "tests/svg",
    "tests/browser_compatibility",
    "tests/error_handling",
    "tests/theme",
    "tests/export",
    "tests/remote",
    "tests/performance",
    "tests/integration"
]

for dir_path in dirs:
    files = glob.glob(os.path.join(dir_path, "test_*.py"))
    for file_path in files:
        with open(file_path, "r") as f:
            content = f.read()
        
        # Replace sys.path.insert line with correct relative path
        new_content = re.sub(
            r"sys\.path\.insert\(0, os\.path\.join\(os\.path\.dirname\(__file__\), '\.'.*",
            "sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))",
            content
        )
        
        with open(file_path, "w") as f:
            f.write(new_content)
        
        print(f"Fixed imports in {file_path}")

print("Import paths fixed in all test files.")
