#!/usr/bin/env python3
"""
Test export enhancement functionality.
"""

import sys
import os
import tempfile
import json
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from mermaid_render.renderers.svg_renderer import SVGRenderer
from unittest.mock import patch, Mock
import requests
from pathlib import Path

def test_enhanced_svg_export():
    """Test enhanced SVG export functionality."""
    print("Testing enhanced SVG export...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        renderer = SVGRenderer(use_local=False)
        
        mock_svg = '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="100"><rect width="100" height="50"/></svg>'
        
        with patch.object(requests.Session, 'get') as mock_get:
            mock_response = Mock()
            mock_response.text = mock_svg
            mock_response.status_code = 200
            mock_response.headers = {'content-type': 'image/svg+xml'}
            mock_response.raise_for_status = Mock()
            mock_get.return_value = mock_response
            
            output_path = Path(temp_dir) / "test.svg"
            
            # Test enhanced export
            export_info = renderer.render_to_file(
                'flowchart TD\n    A --> B',
                str(output_path),
                format='svg',
                add_metadata=True
            )
            
            print(f"Export info: {export_info}")
            assert export_info['success'] == True
            assert export_info['format'] == 'svg'
            assert export_info['size_bytes'] > 0
            assert output_path.exists()
            
            # Check file content
            with open(output_path, 'r') as f:
                content = f.read()
                assert '<svg' in content
                assert 'Generated by mermaid-render' in content  # Metadata
            
            print("✓ Enhanced SVG export passed")

def test_html_export():
    """Test HTML export functionality."""
    print("\nTesting HTML export...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        renderer = SVGRenderer(use_local=False)
        
        mock_svg = '<svg xmlns="http://www.w3.org/2000/svg"><rect/></svg>'
        
        with patch.object(requests.Session, 'get') as mock_get:
            mock_response = Mock()
            mock_response.text = mock_svg
            mock_response.status_code = 200
            mock_response.headers = {'content-type': 'image/svg+xml'}
            mock_response.raise_for_status = Mock()
            mock_get.return_value = mock_response
            
            output_path = Path(temp_dir) / "test.html"
            
            export_info = renderer.render_to_file(
                'flowchart TD\n    A --> B',
                str(output_path),
                format='html',
                theme='dark'
            )
            
            assert export_info['success'] == True
            assert export_info['format'] == 'html'
            assert output_path.exists()
            
            # Check HTML content
            with open(output_path, 'r') as f:
                content = f.read()
                assert '<!DOCTYPE html>' in content
                assert '<svg' in content
                assert 'flowchart TD' in content  # Source code
                assert 'dark' in content  # Theme info
            
            print("✓ HTML export passed")

def test_background_addition():
    """Test background addition to SVG."""
    print("\nTesting background addition...")
    
    renderer = SVGRenderer()
    
    # Test adding background
    svg_content = '<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><rect/></svg>'
    svg_with_bg = renderer._add_background_to_svg(svg_content, '#f0f0f0')
    
    print(f"SVG with background: {svg_with_bg}")
    assert 'fill="#f0f0f0"' in svg_with_bg
    assert '<rect width="100" height="100"' in svg_with_bg
    print("✓ Background addition passed")

def test_export_templates():
    """Test export template functionality."""
    print("\nTesting export templates...")
    
    renderer = SVGRenderer()
    
    # Test getting template
    web_template = renderer.create_export_template('web')
    print(f"Web template: {web_template}")
    assert web_template['format'] == 'svg'
    assert web_template['optimize'] == True
    print("✓ Web template passed")
    
    print_template = renderer.create_export_template('print')
    print(f"Print template: {print_template}")
    assert print_template['format'] == 'pdf'
    assert print_template['background'] == 'white'
    print("✓ Print template passed")
    
    # Test invalid template
    try:
        renderer.create_export_template('invalid')
        assert False, "Should have raised error"
    except ValueError as e:
        assert "Unknown template" in str(e)
        print("✓ Invalid template validation passed")

def test_template_export():
    """Test export with template."""
    print("\nTesting template export...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        renderer = SVGRenderer(use_local=False)
        
        mock_svg = '<svg xmlns="http://www.w3.org/2000/svg"><rect/></svg>'
        
        with patch.object(requests.Session, 'get') as mock_get:
            mock_response = Mock()
            mock_response.text = mock_svg
            mock_response.status_code = 200
            mock_response.headers = {'content-type': 'image/svg+xml'}
            mock_response.raise_for_status = Mock()
            mock_get.return_value = mock_response
            
            output_path = Path(temp_dir) / "test_template.svg"
            
            # Test export with web template
            export_info = renderer.export_with_template(
                'flowchart TD\n    A --> B',
                str(output_path),
                'web',
                theme='dark'
            )
            
            assert export_info['success'] == True
            assert output_path.exists()
            print("✓ Template export passed")

def test_batch_export():
    """Test batch export functionality."""
    print("\nTesting batch export...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        renderer = SVGRenderer(use_local=False)
        
        mock_svg = '<svg xmlns="http://www.w3.org/2000/svg"><rect/></svg>'
        
        with patch.object(requests.Session, 'get') as mock_get:
            mock_response = Mock()
            mock_response.text = mock_svg
            mock_response.status_code = 200
            mock_response.headers = {'content-type': 'image/svg+xml'}
            mock_response.raise_for_status = Mock()
            mock_get.return_value = mock_response
            
            # Prepare batch diagrams
            diagrams = [
                {
                    'name': 'flowchart1',
                    'code': 'flowchart TD\n    A --> B',
                    'theme': 'dark'
                },
                {
                    'name': 'flowchart2',
                    'code': 'flowchart LR\n    C --> D',
                    'theme': 'light'
                },
                {
                    'name': 'sequence1',
                    'code': 'sequenceDiagram\n    A->>B: Hello'
                }
            ]
            
            # Test batch export
            results = renderer.batch_export(
                diagrams,
                temp_dir,
                format='svg',
                naming_pattern='{name}_{theme}'
            )
            
            print(f"Batch export results: {results}")
            assert results['total'] == 3
            assert results['successful'] == 3
            assert results['failed'] == 0
            assert len(results['files']) == 3
            
            # Check files were created
            for file_info in results['files']:
                file_path = Path(file_info['path'])
                assert file_path.exists()
                assert file_path.suffix == '.svg'
            
            print("✓ Batch export passed")

def test_export_error_handling():
    """Test export error handling."""
    print("\nTesting export error handling...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        renderer = SVGRenderer(use_local=False)
        
        # Test unsupported format
        try:
            renderer.render_to_file(
                'flowchart TD\n    A --> B',
                str(Path(temp_dir) / "test.xyz"),
                format='xyz'
            )
            assert False, "Should have raised error"
        except Exception as e:
            assert "Unsupported export format" in str(e)
            print("✓ Unsupported format error handling passed")

def test_png_pdf_export_fallback():
    """Test PNG/PDF export fallback when cairosvg is not available."""
    print("\nTesting PNG/PDF export fallback...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        renderer = SVGRenderer(use_local=False)
        
        mock_svg = '<svg xmlns="http://www.w3.org/2000/svg"><rect/></svg>'
        
        with patch.object(requests.Session, 'get') as mock_get:
            mock_response = Mock()
            mock_response.text = mock_svg
            mock_response.status_code = 200
            mock_response.headers = {'content-type': 'image/svg+xml'}
            mock_response.raise_for_status = Mock()
            mock_get.return_value = mock_response
            
            # Test PNG export without cairosvg
            try:
                renderer.render_to_file(
                    'flowchart TD\n    A --> B',
                    str(Path(temp_dir) / "test.png"),
                    format='png'
                )
                # If cairosvg is available, this should succeed
                print("✓ PNG export succeeded (cairosvg available)")
            except Exception as e:
                # If cairosvg is not available, should get helpful error
                assert "cairosvg" in str(e).lower()
                print("✓ PNG export fallback error handling passed")

if __name__ == "__main__":
    print("Testing export enhancements...")
    
    test_enhanced_svg_export()
    test_html_export()
    test_background_addition()
    test_export_templates()
    test_template_export()
    test_batch_export()
    test_export_error_handling()
    test_png_pdf_export_fallback()
    
    print("\n🎉 All export enhancement tests passed!")
