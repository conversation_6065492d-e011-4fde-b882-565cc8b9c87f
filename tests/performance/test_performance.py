"""
Performance and load tests for mermaid-render.
"""

import time
import threading
import concurrent.futures
from unittest.mock import Mock, patch
import pytest

from mermaid_render import (
    MermaidRenderer,
    FlowchartDiagram,
    SequenceDiagram,
    ClassDiagram,
)


class TestRenderingPerformance:
    """Test rendering performance with various diagram sizes."""

    def test_small_diagram_performance(self):
        """Test performance with small diagrams."""
        diagram = FlowchartDiagram()
        diagram.add_node("A", "Start")
        diagram.add_node("B", "End")
        diagram.add_edge("A", "B")

        renderer = MermaidRenderer()
        
        with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
            mock_obj = Mock()
            mock_obj.__str__ = Mock(return_value="<svg>small diagram</svg>")
            mock_mermaid.return_value = mock_obj

            start_time = time.time()
            result = renderer.render(diagram, format="svg")
            end_time = time.time()

            assert result == "<svg>small diagram</svg>"
            # Small diagrams should render very quickly
            assert (end_time - start_time) < 0.1

    def test_medium_diagram_performance(self):
        """Test performance with medium-sized diagrams."""
        diagram = FlowchartDiagram()
        
        # Create a medium-sized diagram (20 nodes)
        for i in range(20):
            diagram.add_node(f"node_{i}", f"Node {i}")
            if i > 0:
                diagram.add_edge(f"node_{i-1}", f"node_{i}")

        renderer = MermaidRenderer()
        
        with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
            mock_obj = Mock()
            mock_obj.__str__ = Mock(return_value="<svg>medium diagram</svg>")
            mock_mermaid.return_value = mock_obj

            start_time = time.time()
            result = renderer.render(diagram, format="svg")
            end_time = time.time()

            assert result == "<svg>medium diagram</svg>"
            # Medium diagrams should still render quickly
            assert (end_time - start_time) < 0.5

    def test_large_diagram_performance(self):
        """Test performance with large diagrams."""
        diagram = FlowchartDiagram()
        
        # Create a large diagram (100 nodes)
        for i in range(100):
            diagram.add_node(f"node_{i}", f"Node {i}")
            if i > 0:
                diagram.add_edge(f"node_{i-1}", f"node_{i}")
            # Add some branching
            if i % 10 == 0 and i > 0:
                diagram.add_node(f"branch_{i}", f"Branch {i}")
                diagram.add_edge(f"node_{i}", f"branch_{i}")

        renderer = MermaidRenderer()
        
        with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
            mock_obj = Mock()
            mock_obj.__str__ = Mock(return_value="<svg>large diagram</svg>")
            mock_mermaid.return_value = mock_obj

            start_time = time.time()
            result = renderer.render(diagram, format="svg")
            end_time = time.time()

            assert result == "<svg>large diagram</svg>"
            # Large diagrams should render within reasonable time
            assert (end_time - start_time) < 2.0

    def test_complex_sequence_diagram_performance(self):
        """Test performance with complex sequence diagrams."""
        diagram = SequenceDiagram()
        
        # Add multiple participants
        participants = ["User", "Frontend", "API", "Database", "Cache", "Logger"]
        for participant in participants:
            diagram.add_participant(participant.lower(), participant)

        # Add many interactions
        for i in range(50):
            source = participants[i % len(participants)]
            target = participants[(i + 1) % len(participants)]
            diagram.add_message(source.lower(), target.lower(), f"Message {i}")

        renderer = MermaidRenderer()
        
        with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
            mock_obj = Mock()
            mock_obj.__str__ = Mock(return_value="<svg>complex sequence</svg>")
            mock_mermaid.return_value = mock_obj

            start_time = time.time()
            result = renderer.render(diagram, format="svg")
            end_time = time.time()

            assert result == "<svg>complex sequence</svg>"
            # Complex sequence diagrams should render within reasonable time
            assert (end_time - start_time) < 1.5


class TestConcurrentRendering:
    """Test concurrent rendering scenarios."""

    def test_concurrent_small_diagrams(self):
        """Test rendering multiple small diagrams concurrently."""
        def render_diagram(diagram_id):
            diagram = FlowchartDiagram()
            diagram.add_node(f"start_{diagram_id}", f"Start {diagram_id}")
            diagram.add_node(f"end_{diagram_id}", f"End {diagram_id}")
            diagram.add_edge(f"start_{diagram_id}", f"end_{diagram_id}")

            renderer = MermaidRenderer()
            
            with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
                mock_obj = Mock()
                mock_obj.__str__ = Mock(return_value=f"<svg>diagram {diagram_id}</svg>")
                mock_mermaid.return_value = mock_obj

                return renderer.render(diagram, format="svg")

        # Test concurrent rendering with ThreadPoolExecutor
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(render_diagram, i) for i in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        end_time = time.time()

        assert len(results) == 10
        assert all("diagram" in result for result in results)
        # Concurrent rendering should be efficient
        assert (end_time - start_time) < 2.0

    def test_concurrent_different_formats(self):
        """Test rendering same diagram in different formats concurrently."""
        diagram = FlowchartDiagram()
        diagram.add_node("A", "Node A")
        diagram.add_node("B", "Node B")
        diagram.add_edge("A", "B")

        def render_format(format_type):
            renderer = MermaidRenderer()
            
            with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
                mock_obj = Mock()
                mock_obj.__str__ = Mock(return_value=f"<{format_type}>content</{format_type}>")
                mock_mermaid.return_value = mock_obj

                return renderer.render(diagram, format="svg")  # Only SVG supported in current implementation

        formats = ["svg"] * 5  # Test multiple SVG renders concurrently
        
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(render_format, fmt) for fmt in formats]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        end_time = time.time()

        assert len(results) == 5
        assert all("content" in result for result in results)
        # Concurrent format rendering should be efficient
        assert (end_time - start_time) < 1.5


class TestMemoryUsage:
    """Test memory usage patterns."""

    def test_large_diagram_memory_usage(self):
        """Test memory usage with large diagrams."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss

        # Create a very large diagram
        diagram = FlowchartDiagram()
        for i in range(500):
            diagram.add_node(f"node_{i}", f"Node {i} with some longer text content")
            if i > 0:
                diagram.add_edge(f"node_{i-1}", f"node_{i}")

        # Generate Mermaid code (this is where memory usage would be highest)
        mermaid_code = diagram.to_mermaid()
        
        current_memory = process.memory_info().rss
        memory_increase = current_memory - initial_memory

        # Verify the diagram was created successfully
        assert len(mermaid_code) > 10000  # Should be substantial
        assert "node_499" in mermaid_code
        
        # Memory increase should be reasonable (less than 50MB for this test)
        assert memory_increase < 50 * 1024 * 1024

    def test_multiple_diagrams_memory_cleanup(self):
        """Test that memory is properly cleaned up when creating multiple diagrams."""
        import psutil
        import os
        import gc
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss

        # Create and discard multiple diagrams
        for iteration in range(10):
            diagram = FlowchartDiagram()
            for i in range(50):
                diagram.add_node(f"node_{iteration}_{i}", f"Node {iteration}-{i}")
                if i > 0:
                    diagram.add_edge(f"node_{iteration}_{i-1}", f"node_{iteration}_{i}")
            
            # Generate code and then discard
            mermaid_code = diagram.to_mermaid()
            del diagram
            del mermaid_code
            
            # Force garbage collection
            gc.collect()

        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory

        # Memory increase should be minimal after cleanup (less than 20MB)
        assert memory_increase < 20 * 1024 * 1024


class TestScalabilityLimits:
    """Test scalability limits and edge cases."""

    def test_maximum_nodes_handling(self):
        """Test handling of diagrams with maximum reasonable number of nodes."""
        diagram = FlowchartDiagram()
        
        # Test with 1000 nodes (stress test)
        max_nodes = 1000
        for i in range(max_nodes):
            diagram.add_node(f"n{i}", f"Node {i}")
            if i > 0:
                diagram.add_edge(f"n{i-1}", f"n{i}")

        # Should be able to generate Mermaid code without crashing
        start_time = time.time()
        mermaid_code = diagram.to_mermaid()
        end_time = time.time()

        assert isinstance(mermaid_code, str)
        assert len(mermaid_code) > 50000  # Should be very large
        assert f"n{max_nodes-1}" in mermaid_code
        # Should complete within reasonable time even for large diagrams
        assert (end_time - start_time) < 5.0

    def test_deep_nesting_performance(self):
        """Test performance with deeply nested structures."""
        diagram = ClassDiagram()
        
        # Create deeply nested class hierarchy
        for i in range(50):
            class_name = f"Class{i}"
            attributes = [f"attr{j}: str" for j in range(5)]
            methods = [f"method{j}()" for j in range(5)]
            diagram.add_class(class_name, attributes, methods)
            
            if i > 0:
                parent_class = f"Class{i-1}"
                diagram.add_relationship(class_name, parent_class, "inheritance")

        start_time = time.time()
        mermaid_code = diagram.to_mermaid()
        end_time = time.time()

        assert isinstance(mermaid_code, str)
        assert "Class49" in mermaid_code
        assert "inheritance" in mermaid_code
        # Deep nesting should still be handled efficiently
        assert (end_time - start_time) < 2.0

    def test_wide_branching_performance(self):
        """Test performance with wide branching structures."""
        diagram = FlowchartDiagram()
        
        # Create a root node
        diagram.add_node("root", "Root")
        
        # Create wide branching (100 direct children)
        for i in range(100):
            child_node = f"child_{i}"
            diagram.add_node(child_node, f"Child {i}")
            diagram.add_edge("root", child_node)
            
            # Each child has a few sub-children
            for j in range(3):
                grandchild = f"grandchild_{i}_{j}"
                diagram.add_node(grandchild, f"Grandchild {i}-{j}")
                diagram.add_edge(child_node, grandchild)

        start_time = time.time()
        mermaid_code = diagram.to_mermaid()
        end_time = time.time()

        assert isinstance(mermaid_code, str)
        assert "child_99" in mermaid_code
        assert "grandchild_99_2" in mermaid_code
        # Wide branching should be handled efficiently
        assert (end_time - start_time) < 3.0


class TestBenchmarking:
    """Benchmarking tests for performance regression detection."""

    def test_baseline_rendering_benchmark(self):
        """Establish baseline performance metrics for rendering."""
        diagram = FlowchartDiagram()
        
        # Standard test diagram
        for i in range(20):
            diagram.add_node(f"node_{i}", f"Node {i}")
            if i > 0:
                diagram.add_edge(f"node_{i-1}", f"node_{i}")

        renderer = MermaidRenderer()
        
        with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
            mock_obj = Mock()
            mock_obj.__str__ = Mock(return_value="<svg>benchmark diagram</svg>")
            mock_mermaid.return_value = mock_obj

            # Run multiple iterations to get average
            times = []
            for _ in range(10):
                start_time = time.time()
                result = renderer.render(diagram, format="svg")
                end_time = time.time()
                times.append(end_time - start_time)

            avg_time = sum(times) / len(times)
            max_time = max(times)
            min_time = min(times)

            # Baseline expectations
            assert avg_time < 0.1  # Average should be very fast
            assert max_time < 0.2  # Even slowest should be reasonable
            assert min_time > 0.0  # Should take some measurable time

    def test_diagram_generation_benchmark(self):
        """Benchmark diagram generation (Mermaid code creation)."""
        sizes = [10, 50, 100, 200]
        results = {}

        for size in sizes:
            diagram = FlowchartDiagram()
            for i in range(size):
                diagram.add_node(f"node_{i}", f"Node {i}")
                if i > 0:
                    diagram.add_edge(f"node_{i-1}", f"node_{i}")

            start_time = time.time()
            mermaid_code = diagram.to_mermaid()
            end_time = time.time()

            results[size] = end_time - start_time

        # Performance should scale reasonably
        assert results[10] < 0.01
        assert results[50] < 0.05
        assert results[100] < 0.1
        assert results[200] < 0.2

        # Verify linear or sub-linear scaling
        # Time for 200 nodes should be less than 20x time for 10 nodes
        assert results[200] < results[10] * 20
